<script setup lang="ts">
import { ref, watch, onUnmounted, computed } from 'vue'

// Props
interface Props {
  modelValue?: string
  placeholder?: string
  disabled?: boolean
  maxLength?: number
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  placeholder: '聊聊你的情绪...',
  disabled: false,
  maxLength: 1000
})

// Emits
interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'send', message: string): void
  (e: 'send-audio', duration: number): void  // 发送语音消息
  (e: 'voice-start'): void  // 语音录音开始
  (e: 'voice-end'): void    // 语音录音结束
  (e: 'start-recording'): void  // 开始录音（传递给父组件处理）
  (e: 'stop-recording'): void   // 停止录音（传递给父组件处理）
  (e: 'cancel-recording'): void // 取消录音（传递给父组件处理）
}

const emit = defineEmits<Emits>()

// 响应式数据
const inputValue = computed({
  get: () => props.modelValue,
  set: (value: string) => emit('update:modelValue', value)
})
const inputMode = ref<'text' | 'voice'>('text') // 输入模式：文本或语音
const isRecording = ref(false) // 是否正在录音
const recordingTime = ref(0)

// 录音相关（目前仅用于样式演示）
let recordTimer: number | null = null
// TODO: 实际录音功能扩展时需要的变量
// let audioRecorder: MediaRecorder | null = null
// let audioChunks: Blob[] = []

// 监听 props 变化
watch(() => props.modelValue, (newValue) => {
  inputValue.value = newValue
})

// 监听输入值变化
watch(inputValue, (newValue) => {
  emit('update:modelValue', newValue)
})

// 切换输入模式
const toggleInputMode = (): void => {
  if (props.disabled) return
  inputMode.value = inputMode.value === 'text' ? 'voice' : 'text'
}

// 发送消息
const handleSend = () => {
  if (inputValue.value.trim()) {
    emit('send', inputValue.value)
  }
}

// 处理键盘事件
const handleKeyDown = (event: KeyboardEvent) => {
  // Enter键发送消息，Shift+Enter换行
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault() // 阻止默认的换行行为
    handleSend()
  }
  // Shift+Enter允许换行，保持默认行为
  // 不需要额外处理，van-field的textarea会自动处理换行
}

// 开始录音
const startRecording = (): void => {
  if (inputMode.value !== 'voice' || props.disabled) return

  isRecording.value = true
  recordingTime.value = 0
  emit('voice-start')
  emit('start-recording')

  // 录音计时
  recordTimer = window.setInterval(() => {
    recordingTime.value++
    // 最长录音时间限制 (60秒)
    if (recordingTime.value >= 60) {
      stopRecording()
    }
  }, 1000)

  console.log('开始录音')
}

// 停止录音
const stopRecording = (): void => {
  if (inputMode.value !== 'voice' || !isRecording.value) return

  if (recordTimer) {
    clearInterval(recordTimer)
    recordTimer = null
  }

  const duration = recordingTime.value
  isRecording.value = false

  // 只有录音时长大于1秒才发送语音消息
  if (duration >= 1) {
    emit('send-audio', duration)
    emit('stop-recording')
  } else {
    console.log('录音时间太短，至少需要1秒')
    emit('cancel-recording')
  }

  emit('voice-end')
  console.log(`录音结束，时长: ${duration}s`)
}

// 组件卸载时清理
onUnmounted(() => {
  if (recordTimer) {
    clearInterval(recordTimer)
    recordTimer = null
  }
})
</script>

<template>
  <div class="chat-input-wrapper">
    <div class="input-area">
      <div class="input-container">
        <!-- 语音/键盘切换图标 -->
        <div
          v-show="!(inputMode === 'voice' && isRecording)"
          class="voice-icon"
          @click="toggleInputMode"
        >
          <img
            v-if="inputMode === 'text'"
            src="@/assets/icon/chat-voice.svg"
            alt="语音输入"
            class="icon-img"
          />
          <img
            v-else
            src="@/assets/icon/chat-keyboard.svg"
            alt="键盘输入"
            class="icon-img"
          />
        </div>

        <!-- 文本输入模式 -->
        <template v-if="inputMode === 'text'">
          <van-field
            v-model="inputValue"
            type="textarea"
            :placeholder="placeholder"
            class="message-input"
            style="flex-grow: 1;"
            rows="1"
            autosize
            input-align="left"
            :disabled="disabled"
            @keydown="handleKeyDown"
          />
          <van-button
            type="primary"
            size="small"
            :disabled="inputValue.trim().length === 0 || disabled"
            @click="handleSend"
            class="send-button"
          >
            发送
          </van-button>
        </template>

        <!-- 语音输入模式 -->
        <template v-else>
          <div
            class="voice-input-area"
            :class="{ 'recording': isRecording }"
            @touchstart="startRecording"
            @touchend="stopRecording"
            @mousedown="startRecording"
            @mouseup="stopRecording"
            @mouseleave="stopRecording"
          >
            <span v-if="!isRecording" class="voice-hint">按住 说话</span>
            <span v-else class="recording-hint">
              <span class="recording-text">录音中... {{ recordingTime }}s</span>
              <span class="release-text">松开发送</span>
            </span>
          </div>
        </template>
      </div>
    </div>
    <div class="safe-area-bottom"></div>
  </div>
</template>

<style scoped>
.chat-input-wrapper {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  max-width: 600px;
  margin: 0 auto;
  z-index: 10;
}

.input-area {
  background-color: #F6F6F8;
  padding: 8px 16px;
  border-top: 1px solid #F0EEF4;
}

.safe-area-bottom {
  height: 20px;
  background-color: #F6F6F8;
}

.input-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.voice-icon {
  width: 33px;
  height: 33px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  user-select: none;
}

.icon-img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.message-input {
  flex: 1;
}

/* 确保 van-field 自身作为 van-cell 没有额外的垂直 padding */
:deep(.message-input.van-cell) {
  padding: 0 4px !important;
  border-radius: 6px
}

:deep(.van-field__body) {
  background-color: #FFFFFF;
  padding: 7px 6px; /* 上下总 padding: 14px */
  display: flex;
  align-items: flex-start; /* 当文本为单行时，保持其顶部对齐 */
  min-height: 24px;   /* 目标 body 内容高度: 24px。24px (内容) + 14px (padding) = 38px body 总高度 */
  max-height: 120px; /* autosize 的最大高度 */
  box-sizing: border-box; /* 确保 padding 和 border 计算在 min-height/height 内 */
}

:deep(.van-field__control::placeholder) {
  color: #91919E;
  font-family: 'PingFang SC', sans-serif;
  font-style: normal;
  font-weight: 300;
  font-size: 16px;
  line-height: 22px;
}

:deep(.van-field__control[type="textarea"]) {
  word-wrap: break-word;
  word-break: break-all;
}

.send-button {
  background-color: #5F59FF;
  border-color: #5F59FF;
  border-radius: 6px;
  height: 38px;
  font-size: 16px;
  padding: 4px 12px;
}

:deep(.van-button--primary[disabled]) {
  opacity: 0.5;
}

.voice-input-area {
  flex: 1;
  height: 38px;
  background-color: #5F59FF;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  user-select: none;
  transition: all 0.2s ease;
  padding: 0 20px;
  position: relative;
}

.voice-input-area.recording {
  background-color: #979797;
  justify-content: space-between;
  padding: 0 24px;
  transform: scale(1.02);
}

.voice-hint {
  color: #F0EEF4;
  font-size: 16px;
  font-weight: 400;
  font-family: 'PingFang SC', sans-serif;
}

.recording-hint {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  color: #F0EEF4;
  font-size: 16px;
  font-weight: 400;
  font-family: 'PingFang SC', sans-serif;
}

.recording-text {
  text-align: left;
  flex-shrink: 0;
}

.release-text {
  text-align: right;
  flex-shrink: 0;
}
</style>